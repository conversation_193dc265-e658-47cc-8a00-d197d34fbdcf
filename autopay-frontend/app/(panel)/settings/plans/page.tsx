'use client'

import { DataTable } from '@/components/custom-ui/data-table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useUser } from '@/lib/hooks/useUser'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import { Eye, Loader2, Plus } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import { createColumns } from './components/columns'
import CreatePlanModal from './components/create-plan-modal'

export interface OrganizationPlan {
  id: string
  name: string
  description: string | null
  grace_days: number
  periodicity: number
  periodicity_type: string
  organization_id: string
  features: Array<{
    id: string
    name: string
    consumable: boolean
    quota: boolean
    postpaid: boolean
    pivot: {
      charges: number | null
    }
  }>
  subscriptions?: Array<{
    id: string
    subscriber_id: string
    subscriber_type: string
  }>
  created_at: string
  updated_at: string
}

interface OrganizationPlansResponse {
  success: boolean
  code?: number
  locale?: string
  message?: string
  data: {
    data: OrganizationPlan[]
    current_page: number
    per_page: number
    total: number
    last_page: number
  }
}

interface OrganizationUsage {
  usage_stats: Array<{
    feature_id: string
    feature_name: string
    consumed: number
    remaining: number | null
    quota: number | null
    is_unlimited: boolean
    usage_percentage: number
  }>
  is_pay_as_you_go: boolean
  billable_usage: Array<{
    feature_id: string
    feature_name: string
    consumed: number
    rate: number
    total_cost: number
  }>
}

interface OrganizationUsageResponse {
  success: boolean
  code?: number
  locale?: string
  message?: string
  data: OrganizationUsage
}

export default function PlansPage() {
  const { user } = useUser()
  const [createModalOpen, setCreateModalOpen] = useState(false)

  // Action handlers for plan operations
  const handleEditPlan = (plan: OrganizationPlan) => {
    toast.info(`Chỉnh sửa gói: ${plan.name}`)
    // TODO: Implement edit functionality
  }

  const handleCopyPlan = (plan: OrganizationPlan) => {
    toast.info(`Sao chép gói: ${plan.name}`)
    // TODO: Implement copy functionality
  }

  const handleToggleStatus = (plan: OrganizationPlan) => {
    toast.info(`Thay đổi trạng thái gói: ${plan.name}`)
    // TODO: Implement toggle status functionality
  }

  const handleDeletePlan = (plan: OrganizationPlan) => {
    toast.info(`Xóa gói: ${plan.name}`)
    // TODO: Implement delete functionality with confirmation
  }

  // Create columns with action handlers
  const columns = createColumns({
    onEdit: handleEditPlan,
    onCopy: handleCopyPlan,
    onToggleStatus: handleToggleStatus,
    onDelete: handleDeletePlan,
  })

  // Fetch organization plans
  const {
    data: orgPlansData,
    isLoading: orgPlansLoading,
    error: orgPlansError,
    refetch: refetchOrgPlans,
  } = useQuery<OrganizationPlansResponse>({
    queryKey: ['getOrganizationPlans', user?.current_organization?.id],
    queryFn: () => queryFetchHelper(`/organizations/${user?.current_organization?.id}/plans/organization`),
    enabled: !!user?.current_organization?.id,
  })

  // Fetch organization usage statistics
  const {
    data: usageData,
    isLoading: usageLoading,
    error: usageError,
  } = useQuery<OrganizationUsageResponse>({
    queryKey: ['getOrganizationUsage', user?.current_organization?.id],
    queryFn: () =>
      queryFetchHelper(`/organizations/${user?.current_organization?.id}/subscriptions/organization/usage`),
    enabled: !!user?.current_organization?.id,
  })

  const renderOrganizationPlans = () => {
    if (orgPlansError) {
      return (
        <Alert variant="destructive">
          <AlertDescription>Không thể tải danh sách gói dịch vụ. Vui lòng thử lại sau.</AlertDescription>
        </Alert>
      )
    }

    // Only get plans data if orgPlansData exists, otherwise use empty array
    const plans = orgPlansData?.data?.data || []

    return (
      <DataTable
        columns={columns}
        data={plans}
        isLoading={orgPlansLoading || !orgPlansData}
        emptyMessage="Chưa có gói dịch vụ nào. Tạo gói dịch vụ đầu tiên để bắt đầu quản lý thành viên."
        loadingRows={3}
      />
    )
  }

  const renderUsageStats = () => {
    if (usageLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="mr-2 h-6 w-6 animate-spin" />
          <span>Đang tải thống kê sử dụng...</span>
        </div>
      )
    }

    if (usageError) {
      return (
        <Alert variant="destructive">
          <AlertDescription>Không thể tải thống kê sử dụng. Vui lòng thử lại sau.</AlertDescription>
        </Alert>
      )
    }

    const usage = usageData?.data

    // Only show empty state if data has been loaded and is actually empty
    if (usageData && !usage) {
      return (
        <div className="py-8 text-center">
          <div className="bg-muted mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full">
            <Eye className="text-muted-foreground h-8 w-8" />
          </div>
          <h3 className="mb-2 text-lg font-semibold">Chưa có dữ liệu sử dụng</h3>
          <p className="text-muted-foreground">Tổ chức chưa có hoạt động sử dụng nào được ghi nhận</p>
        </div>
      )
    }

    // Return null if data hasn't been loaded yet (will show loading state)
    if (!usage) {
      return null
    }

    return (
      <>
        {/* Current Usage Stats */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Sử dụng hiện tại</CardTitle>
            <CardDescription className="text-xs">
              {usage.is_pay_as_you_go ? 'Pay-as-you-go' : 'Gói cố định'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {usage.usage_stats.map((stat) => (
                <div
                  key={stat.feature_id}
                  className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{stat.feature_name}</span>
                    <div className="text-right">
                      <div className="text-xs font-medium">
                        {stat.consumed} / {stat.quota === null ? '∞' : stat.quota}
                      </div>
                    </div>
                  </div>
                  {stat.quota !== null && (
                    <div className="bg-muted h-1.5 w-full rounded-full">
                      <div
                        className="bg-primary h-1.5 rounded-full transition-all"
                        style={{ width: `${Math.min(stat.usage_percentage, 100)}%` }}
                      />
                    </div>
                  )}
                  {stat.quota !== null && (
                    <div className="text-muted-foreground text-xs">{stat.usage_percentage.toFixed(1)}% đã sử dụng</div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="h-4"></div>

        {/* Billable Usage (for pay-as-you-go) */}
        {usage.is_pay_as_you_go && usage.billable_usage.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Chi phí sử dụng</CardTitle>
              <CardDescription className="text-xs">Tính phí theo thực tế</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {usage.billable_usage.map((billing) => (
                  <div
                    key={billing.feature_id}
                    className="flex items-center justify-between rounded border p-2">
                    <div>
                      <div className="text-sm font-medium">{billing.feature_name}</div>
                      <div className="text-muted-foreground text-xs">
                        {billing.consumed} × {billing.rate.toLocaleString('vi-VN')}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold">{billing.total_cost.toLocaleString('vi-VN')} ₫</div>
                    </div>
                  </div>
                ))}
                <div className="border-t pt-2">
                  <div className="flex items-center justify-between text-sm font-semibold">
                    <span>Tổng:</span>
                    <span>
                      {usage.billable_usage
                        .reduce((sum, billing) => sum + billing.total_cost, 0)
                        .toLocaleString('vi-VN')}{' '}
                      ₫
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="h-4"></div>

        {/* Usage Levels Table */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Bảng tính phí</CardTitle>
            <CardDescription className="text-xs">Mức phí theo usage</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-muted-foreground grid grid-cols-3 gap-2 border-b pb-2 text-xs font-medium">
                <span>Level</span>
                <span>Khoảng</span>
                <span className="text-right">Giá (₫)</span>
              </div>
              <div className="space-y-1">
                <div className="grid grid-cols-3 gap-2 py-1 text-xs">
                  <span>1</span>
                  <span>0-100</span>
                  <span className="text-right">0</span>
                </div>
                <div className="grid grid-cols-3 gap-2 py-1 text-xs">
                  <span>2</span>
                  <span>101-500</span>
                  <span className="text-right">500</span>
                </div>
                <div className="grid grid-cols-3 gap-2 py-1 text-xs">
                  <span>3</span>
                  <span>501-1K</span>
                  <span className="text-right">400</span>
                </div>
                <div className="grid grid-cols-3 gap-2 py-1 text-xs">
                  <span>4</span>
                  <span>1K+</span>
                  <span className="text-right">300</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Quản lý Gói dịch vụ</h3>
        <p className="text-muted-foreground text-sm">Tạo và quản lý các gói dịch vụ cho thành viên trong tổ chức</p>
      </div>

      {/* Two Column Layout */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Left Column - Organization Plans */}
        <div className="space-y-4 lg:col-span-2">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-base font-medium">Gói tổ chức</h4>
              <p className="text-muted-foreground text-sm">{orgPlansData?.data?.total || 0} gói dịch vụ</p>
            </div>
            <Button
              onClick={() => setCreateModalOpen(true)}
              size="sm">
              <Plus className="size-4" />
              Tạo gói mới
            </Button>
          </div>

          {renderOrganizationPlans()}
        </div>

        {/* Right Column - Usage Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Thống kê sử dụng</CardTitle>
            <CardDescription className="text-sm">Quota và chi phí hiện tại</CardDescription>
          </CardHeader>
          <CardContent>{renderUsageStats()}</CardContent>
        </Card>
      </div>

      {/* Create Plan Modal */}
      <CreatePlanModal
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        onSuccess={() => {
          // Refetch organization plans after successful creation
          refetchOrgPlans()
        }}
      />
    </div>
  )
}

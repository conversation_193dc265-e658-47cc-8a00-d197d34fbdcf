<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use LucasDotVin\Soulbscription\Enums\PeriodicityType;
use LucasDotVin\Soulbscription\Models\Feature;
use Modules\Core\Models\Plan;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('features', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('name');
            $table->boolean('consumable');
            $table->boolean('quota')->default(false);
            $table->boolean('postpaid')->default(false);
            $table->integer('periodicity')->unsigned()->nullable();
            $table->string('periodicity_type')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

         $transactions = Feature::create([
            'name' => 'transactions',
            'consumable' => true,
            'postpaid' => true,
            'periodicity_type' => PeriodicityType::Month,
            'periodicity' => 1,
        ]);

        $free = Plan::create([
            'organization_id' => null, // System plan
            'name' => 'freemium',
            'description' => 'Gói dịch vụ miễn phí với số lượng giao dịch giới hạn cho mục đích thử nghiệm',
            'periodicity_type' => PeriodicityType::Month,
            'periodicity' => 1,
        ]);

        $free->features()->attach($transactions, [
            'charges' => 50, // 50 transactions per month
        ]);

        $starter = Plan::create([
            'organization_id' => null, // System plan
            'name' => 'pay-as-you-go',
            'description' => 'Pay-as-you-go, gói dịch vụ thanh toán theo sử dụng thực tế',
            'periodicity_type' => PeriodicityType::Month,
            'periodicity' => 1,
            'grace_days' => 1,
        ]);

        $starter->features()->attach($transactions, [
            'charges' => 0, // Unlimited transactions (postpaid billing)
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('features');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            // Add organization_id to distinguish between system plans and organization plans
            $table->foreignUlid('organization_id')
                ->nullable()
                ->after('id')
                ->constrained('organizations')
                ->cascadeOnDelete();

            // Add description field for better plan management
            $table->text('description')->nullable()->after('name');

            // Add indexes for performance
            $table->index(['organization_id']);
            $table->index(['organization_id', 'name']);
            $table->index(['organization_id', 'created_at']);

            // Create unique constraint for plan names within same organization
            // System plans (organization_id = null) can have same names as org plans
            $table->unique(['organization_id', 'name'], 'plans_org_name_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            $table->dropUnique('plans_org_name_unique');
            $table->dropIndex(['organization_id', 'created_at']);
            $table->dropIndex(['organization_id', 'name']);
            $table->dropIndex(['organization_id']);
            $table->dropForeign(['organization_id']);
            $table->dropColumn(['organization_id', 'description']);
        });
    }
};
